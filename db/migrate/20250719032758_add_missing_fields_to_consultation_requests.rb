# frozen_string_literal: true

# Add missing fields from paper consultation form to consultation_requests table
class AddMissingFieldsToConsultationRequests < ActiveRecord::Migration[7.1]
  tag :predeploy

  def change
    change_table :consultation_requests, bulk: true do |t|
      t.string :college_campus_institute, limit: 255
      t.string :department_program, limit: 255
      t.string :semester, limit: 50
      t.string :academic_year, limit: 20
      t.string :place_of_consultation, limit: 255
      t.text :intervention_given
      t.text :referral_made
      t.boolean :students_adviser_agreement, default: false
      t.string :prepared_by_name, limit: 255
      t.string :prepared_by_designation, limit: 255
      t.string :noted_by_program_chair, limit: 255
      t.string :noted_by_college_dean, limit: 255
      t.text :conformance_signature
      t.string :scf_number, limit: 50
    end

    add_index :consultation_requests, :scf_number, unique: true, where: 'scf_number IS NOT NULL'
  end
end
