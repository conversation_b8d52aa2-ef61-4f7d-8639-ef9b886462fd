class AddConsultationSystemConstraints < ActiveRecord::Migration[7.1]
  disable_ddl_transaction!
  tag :predeploy

  def change
    # Add check constraints for valid enum values
    add_check_constraint :consultation_requests,
                         "nature_of_concern IN ('Personal', 'Academic', 'Teacher-related', " \
                         "'Co-students', 'Family', 'Others')",
                         name: 'consultation_requests_nature_of_concern_check'

    add_check_constraint :consultation_requests,
                         "status IN ('pending', 'approved', 'declined', 'completed', 'cancelled')",
                         name: 'consultation_requests_status_check'

    add_check_constraint :consultation_summaries,
                         "concern_type IN ('Personal', 'Academic', 'Teacher-related', " \
                         "'Co-students', 'Family', 'Others')",
                         name: 'consultation_summaries_concern_type_check'

    # Add business logic constraints
    add_check_constraint :consultation_requests,
                         'preferred_datetime > created_at',
                         name: 'consultation_requests_future_datetime_check'
  end
end
