class AddCustomConcernToConsultationRequests < ActiveRecord::Migration[7.1]
  tag :predeploy

  def change
    add_column :consultation_requests, :custom_concern, :text
    add_index :consultation_requests, :custom_concern

    # Update the nature of concern constraint to use "Others" instead of "Other"
    remove_check_constraint :consultation_requests, name: 'consultation_requests_nature_of_concern_check'
    add_check_constraint :consultation_requests,
                         "nature_of_concern IN ('Personal', 'Academic', 'Teacher-related', " \
                         "'Co-students', 'Family', 'Others')",
                         name: 'consultation_requests_nature_of_concern_check'
  end
end
