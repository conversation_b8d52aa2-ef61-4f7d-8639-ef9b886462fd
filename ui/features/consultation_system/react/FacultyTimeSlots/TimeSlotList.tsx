import React from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Badge } from '@instructure/ui-badge'
import { Text } from '@instructure/ui-text'
import { IconEditLine, IconTrashLine, IconClockLine, IconCalendarMonthLine } from '@instructure/ui-icons'
import type { FacultyTimeSlot } from '../types'

interface TimeSlotListProps {
  timeSlots: FacultyTimeSlot[]
  onEdit: (slot: FacultyTimeSlot) => void
  onDelete: (id: string) => void
  loading: boolean
}

const TimeSlotList: React.FC<TimeSlotListProps> = ({
  timeSlots,
  onEdit,
  onDelete,
  loading
}) => {
  const formatTime = (time: string) => {
    try {
      const [hours, minutes] = time.split(':')
      const date = new Date()
      date.setHours(parseInt(hours), parseInt(minutes))
      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return time
    }
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  const groupedSlots = timeSlots.reduce((groups, slot) => {
    if (slot.is_recurring) {
      if (!groups.recurring) groups.recurring = []
      groups.recurring.push(slot)
    } else {
      if (!groups.specific) groups.specific = []
      groups.specific.push(slot)
    }
    return groups
  }, {} as { recurring?: FacultyTimeSlot[], specific?: FacultyTimeSlot[] })

  const sortSlots = (slots: FacultyTimeSlot[]) => {
    return slots.sort((a, b) => {
      if (a.is_recurring && b.is_recurring) {
        const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        const dayDiff = dayOrder.indexOf(a.day_of_week) - dayOrder.indexOf(b.day_of_week)
        if (dayDiff !== 0) return dayDiff
        return a.start_time.localeCompare(b.start_time)
      }
      if (!a.is_recurring && !b.is_recurring) {
        const dateDiff = new Date(a.specific_date!).getTime() - new Date(b.specific_date!).getTime()
        if (dateDiff !== 0) return dateDiff
        return a.start_time.localeCompare(b.start_time)
      }
      return a.is_recurring ? -1 : 1
    })
  }

  const renderTimeSlot = (slot: FacultyTimeSlot) => (
    <View
      key={slot.id}
      as="div"
      background="primary"
      padding="medium"
      borderRadius="medium"
      borderWidth="small"
      borderColor="brand"
      margin="0 0 small 0"
    >
      <View as="div" display="flex" style={{ justifyContent: 'space-between' }}>
        <View as="div" width="70%">
          <View as="div" display="flex" margin="0 0 x-small 0">
            {slot.is_recurring ? (
              <IconCalendarMonthLine size="x-small" />
            ) : (
              <IconClockLine size="x-small" />
            )}
            <View as="div" margin="0 0 0 x-small">
              <Text weight="bold">
                {slot.is_recurring ? slot.day_of_week : formatDate(slot.specific_date!)}
              </Text>
            </View>
            <Badge
              count={slot.pending_requests_count || 0}
              countUntil={99}
              margin="0 0 0 small"
              type={(slot.pending_requests_count || 0) > 0 ? 'notification' : 'count'}
              standalone={true}
            />
          </View>

          <View as="div" margin="0 0 x-small 0">
            <Text size="large" color="brand">
              {formatTime(slot.start_time)} - {formatTime(slot.end_time)}
            </Text>
          </View>

          <View as="div" margin="0 0 x-small 0">
            <Badge
              type={slot.is_available ? 'notification' : 'count'}
              margin="0 x-small 0 0"
            >
              {slot.is_available ? 'Available' : 'Unavailable'}
            </Badge>
            <Badge
              type="count"
              margin="0 0 0 x-small"
            >
              {slot.is_recurring ? 'Recurring' : 'One-time'}
            </Badge>
          </View>

          {slot.notes && (
            <View as="div" margin="small 0 0 0">
              <Text size="small" fontStyle="italic">
                {slot.notes}
              </Text>
            </View>
          )}

          {(slot.pending_requests_count || 0) > 0 && (
            <View as="div" margin="small 0 0 0" background="alert" padding="x-small" borderRadius="small">
              <Text size="small" color="alert">
                {slot.pending_requests_count || 0} pending consultation request{(slot.pending_requests_count || 0) !== 1 ? 's' : ''}
              </Text>
            </View>
          )}
        </View>

        <View as="div" display="flex">
          <View as="div" margin="0 x-small 0 0">
            <Button
              size="small"
              renderIcon={<IconEditLine />}
              onClick={() => onEdit(slot)}
              disabled={loading}
            >
              Edit
            </Button>
          </View>
          <View as="div">
            <Button
              size="small"
              color="danger"
              renderIcon={<IconTrashLine />}
              onClick={() => onDelete(slot.id)}
              disabled={loading || (slot.pending_requests_count || 0) > 0}
            >
              Delete
            </Button>
          </View>
        </View>
      </View>
    </View>
  )

  if (timeSlots.length === 0) {
    return null
  }

  return (
    <View as="div">
      {groupedSlots.recurring && groupedSlots.recurring.length > 0 && (
        <View as="div" margin="0 0 large 0">
          <Heading level="h3" margin="0 0 medium 0">
            Recurring Time Slots
          </Heading>
          {sortSlots(groupedSlots.recurring).map(renderTimeSlot)}
        </View>
      )}

      {groupedSlots.specific && groupedSlots.specific.length > 0 && (
        <View as="div" margin="0 0 large 0">
          <Heading level="h3" margin="0 0 medium 0">
            Specific Date Time Slots
          </Heading>
          {sortSlots(groupedSlots.specific).map(renderTimeSlot)}
        </View>
      )}
    </View>
  )
}

export default TimeSlotList
